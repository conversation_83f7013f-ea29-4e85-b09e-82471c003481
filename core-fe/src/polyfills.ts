/**
 * Polyfills for React 16 compatibility
 */

import * as React from "react";
import { useSyncExternalStore } from "use-sync-external-store/shim";

// Polyfill for useSyncExternalStore (required for Zustand with React 16)
if (!(React as any).useSyncExternalStore) {
  (React as any).useSyncExternalStore = useSyncExternalStore;
}

// Also add to global window.React if it exists
if (
  typeof window !== "undefined" &&
  (window as any).React &&
  !(window as any).React.useSyncExternalStore
) {
  (window as any).React.useSyncExternalStore = useSyncExternalStore;
}

export {};
