import React, { type FC } from "react";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, Divider, Loader, Text } from "@wix/design-system";

import {
  useError,
  useIsLoading,
  useRootActions,
  useRootStore,
  useShopInfo
} from "../hooks/useRoot";

/**
 * Advanced demo component showing different ways to use Zustand store
 */
const ZustandDemo: FC = () => {
  // Method 1: Optimized selectors (better performance)
  const isLoading = useIsLoading();
  const shopInfo = useShopInfo();
  const error = useError();

  // Method 3: Actions
  const { initializeRoot, setLoading, setError, setShopInfo, reset } = useRootActions();

  // Method 4: Direct store access (for advanced usage)
  const storeState = useRootStore();

  const handleTestActions = () => {
    // Demo: Set loading state
    setLoading(true);

    setTimeout(() => {
      setLoading(false);
      setShopInfo({
        shop: {
          id: "demo-shop-123",
          instanceId: "demo-instance-456"
        },
        token: "demo-token-789"
      });
    }, 2000);
  };

  const handleTestError = () => {
    setError(true);
    setTimeout(() => {
      setError(false);
    }, 3000);
  };

  const handleReset = () => {
    reset();
  };

  const handleRefresh = () => {
    initializeRoot();
  };

  return (
    <Card>
      <Card.Header title='🚀 Zustand State Management Demo' />
      <Card.Content>
        <Box direction='vertical' gap={3}>
          {/* Current State Display */}
          <Box>
            <Text size='medium' weight='bold'>
              Current State:
            </Text>
            <Box marginTop={1} direction='vertical' gap={1}>
              <Box align='space-between'>
                <Text>Loading:</Text>
                <Badge type={isLoading ? "warning" : "success"} size='medium'>
                  {isLoading ? "Loading..." : "Ready"}
                </Badge>
              </Box>
              <Box align='space-between'>
                <Text>Error:</Text>
                <Badge type={error ? "danger" : "success"} size='medium'>
                  {error ? "Error" : "OK"}
                </Badge>
              </Box>
              <Box align='space-between'>
                <Text>Shop Info:</Text>
                <Text color={shopInfo ? "D10" : "D20"}>
                  {shopInfo ? "Available" : "Not loaded"}
                </Text>
              </Box>
            </Box>
          </Box>

          <Divider />

          {/* Shop Info Details */}
          {shopInfo && (
            <Box direction='vertical' gap={2}>
              <Text size='medium' weight='bold'>
                Shop Information:
              </Text>
              <Box direction='vertical' gap={1}>
                <Box>
                  <Text weight='bold'>Shop ID: </Text>
                  <Text>{shopInfo.shop?.id || "N/A"}</Text>
                </Box>
                <Box>
                  <Text weight='bold'>Instance ID: </Text>
                  <Text>{shopInfo.shop?.instanceId || "N/A"}</Text>
                </Box>
                <Box>
                  <Text weight='bold'>Token: </Text>
                  <Text>
                    {shopInfo.token ? `${shopInfo.token.substring(0, 10)}...` : "No token"}
                  </Text>
                </Box>
              </Box>
            </Box>
          )}

          {error && (
            <Box>
              <Text color='D10' weight='bold'>
                ❌ Error State Active
              </Text>
              <Text size='small'>This demonstrates error handling in Zustand store</Text>
            </Box>
          )}

          {isLoading && (
            <Box align='center' direction='vertical' gap={2}>
              <Loader size='small' />
              <Text>Loading state demonstration...</Text>
            </Box>
          )}

          <Divider />

          {/* Action Buttons */}
          <Box direction='vertical' gap={2}>
            <Text size='medium' weight='bold'>
              Test Actions:
            </Text>
            <Box direction='horizontal' gap={2}>
              <Button size='small' onClick={handleTestActions} disabled={isLoading}>
                Test Loading & Set Data
              </Button>
              <Button size='small' onClick={handleTestError} disabled={error} skin='destructive'>
                Test Error State
              </Button>
              <Button size='small' onClick={handleRefresh} disabled={isLoading}>
                Refresh (Real API)
              </Button>
              <Button size='small' onClick={handleReset} skin='light'>
                Reset Store
              </Button>
            </Box>
          </Box>

          <Divider />

          {/* Performance Comparison */}
          <Box direction='vertical' gap={2}>
            <Text size='medium' weight='bold'>
              Performance Comparison:
            </Text>
            <Box direction='vertical' gap={1}>
              <Text size='small'>
                <Text weight='bold'>✅ Optimized Selectors:</Text> Components only re-render when
                specific state changes
              </Text>
              <Text size='small'>
                <Text weight='bold'>✅ No Provider Needed:</Text> Direct store access without
                context wrapping
              </Text>
              <Text size='small'>
                <Text weight='bold'>✅ DevTools Support:</Text> Open Redux DevTools to see state
                changes
              </Text>
              <Text size='small'>
                <Text weight='bold'>✅ TypeScript:</Text> Full type safety and autocompletion
              </Text>
            </Box>
          </Box>

          {/* Store Debug Info */}
          <Box direction='vertical' gap={1}>
            <Text size='small' weight='bold'>
              Debug Info (Store State):
            </Text>
            <Box backgroundColor='D80' padding={2} borderRadius='4px'>
              <Text size='tiny' family='monospace'>
                {JSON.stringify(
                  {
                    isLoading: storeState.isLoading,
                    hasShopInfo: !!storeState.shopInfo,
                    error: storeState.error,
                    shopId: storeState.shopInfo?.shop?.id || null
                  },
                  null,
                  2
                )}
              </Text>
            </Box>
          </Box>
        </Box>
      </Card.Content>
    </Card>
  );
};

export default ZustandDemo;
